import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/university',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/university',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/university',
    method: 'put',
    data
  })
}

export function get(params) {
  return request({
    url: 'api/university',
    method: 'get',
    params
  })
}

// 批量导入
export function batchImport(data) {
  return request({
    url: 'api/university/batch-import',
    method: 'post',
    data
  })
}

// 导出
export function exportUniversity(params) {
  return request({
    url: 'api/university/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export default { add, edit, del, get, batchImport, exportUniversity }
