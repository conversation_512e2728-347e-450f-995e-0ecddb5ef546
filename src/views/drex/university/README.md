# 高校域名白名单管理

## 功能说明

这是一个基于 `university-admin.html` 设计的高校域名白名单管理模块，提供以下功能：

1. **域名管理**
   - 新增域名白名单
   - 编辑域名信息
   - 删除域名记录
   - 批量删除

2. **批量导入**
   - 支持CSV文件批量导入
   - 文件格式：学校名称,域名（无表头）
   - 示例：北京大学,pku.edu.cn

3. **搜索功能**
   - 按学校名称搜索
   - 按域名搜索

## 后端配置要求

### 1. 菜单配置

需要在后端管理系统中添加以下菜单配置：

```
父级菜单：DREX管理
菜单名称：高校域名白名单
路由路径：/drex/university
组件路径：drex/university/index
图标：可选择合适的图标
排序：根据需要设置
```

### 2. 权限配置

需要配置以下权限：

- `university:add` - 新增权限
- `university:edit` - 编辑权限  
- `university:del` - 删除权限

### 3. API接口

后端需要提供以下接口：

```
GET    /api/university          - 分页查询
POST   /api/university          - 新增
PUT    /api/university          - 编辑
DELETE /api/university          - 删除
POST   /api/university/batch-import - 批量导入
GET    /api/university/export   - 导出（可选）
```

### 4. 错误处理

后端接口需要返回标准化的错误格式，前端已支持以下错误码：

#### 单条记录操作错误
```json
{
    "errorCode": "UNIVERSITY_EXISTS",
    "message": "学校名称已存在",
    "errorData": "北京大学",
    "timestamp": 1754907442815
}

{
    "errorCode": "DOMAIN_EXISTS",
    "message": "域名已存在",
    "errorData": "pku.edu.cn",
    "timestamp": 1754907647256
}
```

#### 批量导入错误
```json
{
    "errorCode": "BATCH_IMPORT_PARTIAL_FAILURE",
    "message": "部分记录导入失败",
    "errorData": {
        "successCount": 5,
        "failureCount": 2,
        "failures": [
            {"line": 3, "reason": "学校名称已存在", "data": "北京大学"},
            {"line": 7, "reason": "域名格式不正确", "data": "invalid-domain"}
        ]
    },
    "timestamp": 1754907647256
}
```

### 5. 数据库表结构

建议的表结构：

```sql
CREATE TABLE university_whitelist (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    university VARCHAR(255) NOT NULL COMMENT '学校名称',
    domain VARCHAR(255) NOT NULL COMMENT '域名',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_university (university),
    UNIQUE KEY uk_domain (domain)
);
```

## 文件结构

```
src/views/drex/university/
├── index.vue          # 主组件
└── README.md         # 说明文档

src/api/drex/university/
└── university.js     # API接口
```

## 使用说明

1. 确保后端已配置相应的菜单和权限
2. 确保后端API接口已实现
3. 重新登录系统以获取最新的菜单权限
4. 在DREX管理菜单下即可看到"高校域名白名单"选项

## 特性

- 基于项目现有的CRUD框架
- 支持权限控制
- 响应式设计
- 数据验证（域名格式验证）
- 批量操作支持
- 与项目整体风格保持一致
