<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">学校名称</label>
        <el-input v-model="query.university" clearable placeholder="学校名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">域名</label>
        <el-input v-model="query.domain" clearable placeholder="域名" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission">
        <template slot="right">
          <el-button
            v-permission="permission.add"
            class="filter-item"
            size="mini"
            type="success"
            icon="el-icon-upload"
            @click="showImportDialog"
          >
            批量导入
          </el-button>
        </template>
      </crudOperation>
      
      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        width="500px"
      >
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="100px">
          <el-form-item label="学校名称" prop="university">
            <el-input v-model="form.university" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="域名" prop="domain">
            <el-input v-model="form.domain" placeholder="例如: pku.edu.cn" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>

      <!-- 批量导入对话框 -->
      <el-dialog
        title="批量导入域名"
        :visible.sync="importDialogVisible"
        width="600px"
        :close-on-click-modal="false"
      >
        <el-form ref="importForm" :model="importForm" label-width="120px">
          <el-form-item label="上传CSV文件">
            <el-upload
              ref="csvUpload"
              :limit="1"
              :before-upload="beforeUpload"
              :on-change="handleFileChange"
              :auto-upload="false"
              accept=".csv"
              drag
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">
                <p>仅支持CSV文件，文件格式要求：第一列为学校名称，第二列为域名，无表头。</p>
                <p>示例：北京大学,pku.edu.cn</p>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelImport">取消</el-button>
          <el-button type="primary" :loading="importLoading" @click="handleImport">开始导入</el-button>
        </div>
      </el-dialog>

      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="university" label="学校名称" />
        <el-table-column prop="domain" label="域名" />
        <el-table-column prop="updateTime" label="更新时间" :formatter="formatTime" />
        <el-table-column v-if="checkPer(['admin','university:edit','university:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudUniversity from '@/api/drex/university/university'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, university: null, domain: null, updateTime: null }
export default {
  name: 'University',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '高校域名白名单', url: 'api/university', idField: 'id', sort: 'id,desc', crudMethod: { ...crudUniversity }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'university:add'],
        edit: ['admin', 'university:edit'],
        del: ['admin', 'university:del']
      },
      rules: {
        university: [
          { required: true, message: '学校名称不能为空', trigger: 'blur' }
        ],
        domain: [
          { required: true, message: '域名不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && !/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
                callback(new Error('请输入有效的域名格式'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      importDialogVisible: false,
      importForm: {},
      importLoading: false,
      uploadFile: null
    }
  },
  methods: {
    formatTime(row, column, cellValue) {
      if (!cellValue) return '-'
      const date = new Date(cellValue)
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-')
    },
    
    // 显示导入对话框
    showImportDialog() {
      this.importDialogVisible = true
      this.uploadFile = null
    },
    
    // 取消导入
    cancelImport() {
      this.importDialogVisible = false
      this.uploadFile = null
      this.$refs.csvUpload.clearFiles()
    },
    
    // 文件上传前验证
    beforeUpload(file) {
      const isCSV = file.type === 'text/csv' || file.name.endsWith('.csv')
      const isLt5M = file.size / 1024 / 1024 < 5
      
      if (!isCSV) {
        this.$message.error('只能上传CSV文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }
      return false // 阻止自动上传
    },
    
    // 文件选择变化
    handleFileChange(file, fileList) {
      this.uploadFile = file.raw
    },
    
    // 处理导入
    handleImport() {
      if (!this.uploadFile) {
        this.$message.error('请选择要导入的CSV文件')
        return
      }
      
      this.importLoading = true
      const reader = new FileReader()
      
      reader.onload = (event) => {
        try {
          const csvData = event.target.result
          const lines = csvData.split('\n')
          const importData = []
          
          for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim()
            if (!line) continue
            
            const parts = line.split(',')
            if (parts.length !== 2) {
              this.$message.error(`文件第 ${i + 1} 行格式错误，应为：学校名称,域名`)
              this.importLoading = false
              return
            }
            
            const university = parts[0].trim()
            const domain = parts[1].trim()
            
            if (!university || !domain) {
              this.$message.error(`文件第 ${i + 1} 行数据不完整`)
              this.importLoading = false
              return
            }
            
            // 验证域名格式
            if (!/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(domain)) {
              this.$message.error(`文件第 ${i + 1} 行域名格式不正确`)
              this.importLoading = false
              return
            }
            
            importData.push({ university, domain })
          }
          
          if (importData.length === 0) {
            this.$message.error('文件中没有有效数据')
            this.importLoading = false
            return
          }
          
          // 调用批量导入API
          crudUniversity.batchImport(importData).then(response => {
            this.$message.success(`成功导入 ${response.count || importData.length} 条记录`)
            this.crud.refresh()
            this.cancelImport()
          }).catch(error => {
            this.$message.error('导入失败：' + (error.message || '未知错误'))
          }).finally(() => {
            this.importLoading = false
          })
          
        } catch (error) {
          this.$message.error('文件解析失败：' + error.message)
          this.importLoading = false
        }
      }
      
      reader.onerror = () => {
        this.$message.error('文件读取失败')
        this.importLoading = false
      }
      
      reader.readAsText(this.uploadFile, 'UTF-8')
    },
    
    // 钩子：提交前验证
    [CRUD.HOOK.beforeSubmit](crud) {
      // 验证学校名称是否重复
      const university = crud.form.university
      const domain = crud.form.domain
      const currentId = crud.form.id
      
      // 这里可以添加重复性验证逻辑
      return true
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
