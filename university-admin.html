<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TREX 后台管理 - 高校域名白名单</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/heroicons/2.0.18/24/outline/heroicons.min.css" rel="stylesheet">
    <style>
        :root {
            --sidebar-bg: #2d3748;
            --sidebar-text: #a0aec0;
            --sidebar-hover-bg: #4a5568;
            --sidebar-active-bg: #4a5568;
            --sidebar-active-text: #ffffff;
            --main-bg: #f7fafc;
            --border-color: #e2e8f0;
            --primary-color: #4299e1;
            --primary-hover: #2b6cb0;
        }
        body {
            background-color: var(--main-bg);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }
        .sidebar { background-color: var(--sidebar-bg); color: var(--sidebar-text); }
        .sidebar-link:hover { background-color: var(--sidebar-hover-bg); color: var(--sidebar-active-text); }
        .sidebar-link.active { background-color: var(--primary-color); color: var(--sidebar-active-text); }
        .btn-primary { background-color: var(--primary-color); }
        .btn-primary:hover { background-color: var(--primary-hover); }
        .table th { background-color: #f8fafc; }
        .modal-overlay { background-color: rgba(0,0,0,0.5); }
        .fade-in { animation: fadeIn 0.3s ease-out; }
        @keyframes fadeIn { from { opacity: 0; transform: scale(0.95); } to { opacity: 1; transform: scale(1); } }
        .icon {
            width: 1.25rem;
            height: 1.25rem;
            stroke-width: 1.5;
            display: inline-block;
            vertical-align: middle;
            margin-right: 0.75rem;
        }
    </style>
</head>
<body class="text-gray-800">
    <div class="flex h-screen bg-gray-200">
        <!-- Sidebar -->
        <div class="sidebar w-64 flex-shrink-0 flex flex-col space-y-2 p-2 hidden md:block">
            <div class="px-4 py-3 mb-2">
                <h2 class="text-2xl font-semibold text-white">TREX Admin</h2>
            </div>
            <nav class="flex-1">
                <a href="#" class="sidebar-link flex items-center py-2.5 px-4 rounded transition duration-200">
                    <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 010 3.75H5.625a1.875 1.875 0 010-3.75z" /></svg>
                    <span>首页</span>
                </a>
                <a href="#" class="sidebar-link active flex items-center py-2.5 px-4 rounded transition duration-200">
                    <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.286zm0 13.036h.008v.008h-.008v-.008z" /></svg>
                    <span>高校域名白名单</span>
                </a>
                <a href="#" class="sidebar-link flex items-center py-2.5 px-4 rounded transition duration-200">
                    <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" /></svg>
                    <span>用户管理</span>
                </a>
                <a href="#" class="sidebar-link flex items-center py-2.5 px-4 rounded transition duration-200">
                    <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.438.995a6.427 6.427 0 010 1.99l.438.995c.293.24 1.003.827 1.003.827a1.125 1.125 0 01.26 1.431l-1.296 2.247a1.125 1.125 0 01-1.37.49l-1.217-.456c-.355-.133-.75-.072-1.075.124a6.57 6.57 0 01-.22.127c-.332.183-.582.495-.645.87l-.213 1.281c-.09.542-.56.94-1.11-.94h-2.593c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.063-.374-.313-.686-.645-.87a6.52 6.52 0 01-.22-.127c-.324-.196-.72-.257-1.075-.124l-1.217.456a1.125 1.125 0 01-1.37-.49l-1.296-2.247a1.125 1.125 0 01.26-1.431l1.003-.827c.293-.24.438.613-.438.995a6.427 6.427 0 010-1.99l-.438-.995c-.293-.24-1.003-.827-1.003-.827a1.125 1.125 0 01-.26-1.431l1.296-2.247a1.125 1.125 0 011.37-.49l1.217.456c.355.133.75.072 1.075-.124a6.57 6.57 0 01.22-.127c-.332-.183-.582.495-.645-.87l.213-1.281z" /></svg>
                    <span>系统设置</span>
                </a>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <header class="flex justify-between items-center p-4 bg-white border-b border-gray-200">
                <div>
                    <h1 class="text-xl font-semibold">高校域名白名单</h1>
                    <p class="text-sm text-gray-500">首页 / 校园先锋 / 高校域名白名单</p>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm">欢迎, admin</span>
                    <button class="p-2 rounded-full hover:bg-gray-100">
                        <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" /></svg>
                    </button>
                </div>
            </header>

            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-4">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <!-- Search and Actions -->
                    <div class="flex flex-col md:flex-row items-center justify-between mb-4 space-y-2 md:space-y-0">
                        <div class="flex items-center space-x-2">
                            <input id="search-input" type="text" placeholder="输入域名或学校名称搜索" class="w-full md:w-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                            <button id="search-btn" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">查询</button>
                            <button id="reset-btn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">重置</button>
                        </div>
                        <div class="space-x-2">
                            <button id="add-btn" class="px-4 py-2 btn-primary text-white rounded-md hover:bg-blue-600">
                                <span class="mr-1">+</span> 新增域名
                            </button>
                             <button id="import-btn" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">批量导入</button>
                            <button id="batch-delete-btn" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">批量删除</button>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" class="p-4"><input id="select-all" type="checkbox"></th>
                                    <th scope="col" class="px-6 py-3">ID</th>
                                    <th scope="col" class="px-6 py-3">学校名称</th>
                                    <th scope="col" class="px-6 py-3">域名</th>
                                    <th scope="col" class="px-6 py-3">更新时间</th>
                                    <th scope="col" class="px-6 py-3">操作</th>
                                </tr>
                            </thead>
                            <tbody id="table-body">
                                <!-- Rows will be injected by JS -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div id="modal" class="modal-overlay fixed inset-0 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md fade-in">
            <div class="flex justify-between items-center p-4 border-b">
                <h3 id="modal-title" class="text-lg font-semibold">新增域名</h3>
                <button id="close-modal-btn" class="text-gray-400 hover:text-gray-600">&times;</button>
            </div>
            <div class="p-6">
                <form id="modal-form" novalidate>
                    <input type="hidden" id="domain-id">
                    <div class="mb-4">
                        <label for="university-name" class="block text-sm font-medium text-gray-700 mb-1">学校名称</label>
                        <input type="text" id="university-name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" required>
                        <p id="university-error" class="text-red-500 text-sm mt-1 hidden"></p>
                    </div>
                    <div class="mb-4">
                        <label for="domain-name" class="block text-sm font-medium text-gray-700 mb-1">域名</label>
                        <input type="text" id="domain-name" placeholder="例如: pku.edu.cn" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" required>
                        <p id="domain-error" class="text-red-500 text-sm mt-1 hidden"></p>
                    </div>
                    <div class="flex justify-end space-x-2 pt-4">
                        <button type="button" id="cancel-btn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">取消</button>
                        <button type="submit" class="px-4 py-2 btn-primary text-white rounded-md">确定</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Batch Import Modal -->
    <div id="import-modal" class="modal-overlay fixed inset-0 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-lg fade-in">
            <div class="flex justify-between items-center p-4 border-b">
                <h3 class="text-lg font-semibold">批量导入域名</h3>
                <button id="close-import-modal-btn" class="text-gray-400 hover:text-gray-600">&times;</button>
            </div>
            <div class="p-6">
                <form id="import-form">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">上传CSV文件</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="csv-file-input" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>选择文件</span>
                                        <input id="csv-file-input" name="csv-file-input" type="file" class="sr-only" accept=".csv, text/csv">
                                    </label>
                                    <p class="pl-1">或拖拽到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">仅支持CSV文件</p>
                            </div>
                        </div>
                        <p id="file-name-display" class="text-sm text-gray-600 mt-2"></p>
                        <p class="mt-2 text-xs text-gray-500">文件格式要求：第一列为学校名称，第二列为域名，无表头。</p>
                        <p id="import-error" class="text-red-500 text-sm mt-1 hidden"></p>
                    </div>
                    <div class="flex justify-end space-x-2 pt-4">
                        <button type="button" id="cancel-import-btn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">取消</button>
                        <button type="submit" class="px-4 py-2 bg-green-500 text-white rounded-md">开始导入</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div id="delete-confirm-modal" class="modal-overlay fixed inset-0 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-sm fade-in">
             <div class="p-6 text-center">
                <svg class="mx-auto mb-4 w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>
                <h3 class="mb-5 text-lg font-normal text-gray-500">确定要删除吗？</h3>
                <p class="text-sm text-gray-500 mb-5">此操作不可撤销。</p>
                <button id="confirm-delete-btn" class="text-white bg-red-600 hover:bg-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center mr-2">
                    是的，删除
                </button>
                <button id="cancel-delete-btn" class="text-gray-500 bg-white hover:bg-gray-100 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900">
                    取消
                </button>
            </div>
        </div>
    </div>


    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const formatDateTime = (dateString) => {
            const options = {
                year: 'numeric', month: '2-digit', day: '2-digit',
                hour: '2-digit', minute: '2-digit', second: '2-digit',
                hour12: false
            };
            return new Intl.DateTimeFormat('zh-CN', options).format(new Date(dateString));
        };
        
        const getCurrentDateTime = () => new Date().toISOString();

        let whitelistData = [
            { id: 1, university: '北京大学', domain: 'pku.edu.cn', updatedAt: '2023-10-01T10:30:00Z' },
            { id: 2, university: '清华大学', domain: 'tsinghua.edu.cn', updatedAt: '2023-10-01T11:00:00Z' },
            { id: 3, university: '复旦大学', domain: 'fudan.edu.cn', updatedAt: '2023-10-02T14:20:15Z' },
            { id: 4, university: '上海交通大学', domain: 'sjtu.edu.cn', updatedAt: '2023-10-03T09:05:30Z' },
            { id: 5, university: 'Massachusetts Institute of Technology', domain: 'mit.edu', updatedAt: '2023-10-05T18:45:00Z' },
            { id: 6, university: 'Stanford University', domain: 'stanford.edu', updatedAt: '2023-10-05T19:00:00Z' },
        ];

        const tableBody = document.getElementById('table-body');
        const modal = document.getElementById('modal');
        const importModal = document.getElementById('import-modal');
        const deleteConfirmModal = document.getElementById('delete-confirm-modal');
        const modalTitle = document.getElementById('modal-title');
        const modalForm = document.getElementById('modal-form');
        const importForm = document.getElementById('import-form');
        const csvFileInput = document.getElementById('csv-file-input');
        const fileNameDisplay = document.getElementById('file-name-display');
        const domainIdInput = document.getElementById('domain-id');
        const universityNameInput = document.getElementById('university-name');
        const domainNameInput = document.getElementById('domain-name');
        const universityError = document.getElementById('university-error');
        const domainError = document.getElementById('domain-error');
        
        let deleteTarget = null; // Can be a single ID or an array of IDs

        const renderTable = (data) => {
            tableBody.innerHTML = '';
            if (data.length === 0) {
                tableBody.innerHTML = `<tr><td colspan="6" class="text-center py-4">无数据</td></tr>`;
                return;
            }
            data.forEach(item => {
                const row = `
                    <tr class="bg-white border-b hover:bg-gray-50">
                        <td class="w-4 p-4"><input type="checkbox" class="row-checkbox" value="${item.id}"></td>
                        <td class="px-6 py-4 font-medium text-gray-900">${item.id}</td>
                        <td class="px-6 py-4">${item.university}</td>
                        <td class="px-6 py-4">${item.domain}</td>
                        <td class="px-6 py-4">${formatDateTime(item.updatedAt)}</td>
                        <td class="px-6 py-4 space-x-2">
                            <button class="edit-btn font-medium text-blue-600 hover:underline" data-id="${item.id}">编辑</button>
                            <button class="delete-btn font-medium text-red-600 hover:underline" data-id="${item.id}">删除</button>
                        </td>
                    </tr>
                `;
                tableBody.insertAdjacentHTML('beforeend', row);
            });
        };

        const openModal = (mode, data = {}) => {
            modalForm.reset();
            universityError.classList.add('hidden');
            domainError.classList.add('hidden');
            if (mode === 'add') {
                modalTitle.textContent = '新增域名';
                domainIdInput.value = '';
            } else {
                modalTitle.textContent = '编辑域名';
                domainIdInput.value = data.id;
                universityNameInput.value = data.university;
                domainNameInput.value = data.domain;
            }
            modal.classList.remove('hidden');
        };

        const closeModal = () => modal.classList.add('hidden');
        const openImportModal = () => importModal.classList.remove('hidden');
        const closeImportModal = () => {
            importModal.classList.add('hidden');
            importForm.reset();
            fileNameDisplay.textContent = '';
            document.getElementById('import-error').classList.add('hidden');
        };
        const openDeleteModal = (id) => {
            deleteTarget = id;
            deleteConfirmModal.classList.remove('hidden');
        }
        const closeDeleteModal = () => {
            deleteTarget = null;
            deleteConfirmModal.classList.add('hidden');
        }

        document.getElementById('add-btn').addEventListener('click', () => openModal('add'));
        document.getElementById('close-modal-btn').addEventListener('click', closeModal);
        document.getElementById('cancel-btn').addEventListener('click', closeModal);
        
        document.getElementById('import-btn').addEventListener('click', openImportModal);
        document.getElementById('close-import-modal-btn').addEventListener('click', closeImportModal);
        document.getElementById('cancel-import-btn').addEventListener('click', closeImportModal);

        document.getElementById('cancel-delete-btn').addEventListener('click', closeDeleteModal);

        csvFileInput.addEventListener('change', () => {
            if (csvFileInput.files.length > 0) {
                fileNameDisplay.textContent = `已选择文件: ${csvFileInput.files[0].name}`;
            } else {
                fileNameDisplay.textContent = '';
            }
        });

        modalForm.addEventListener('submit', (e) => {
            e.preventDefault();
            
            universityError.classList.add('hidden');
            domainError.classList.add('hidden');

            const id = domainIdInput.value;
            const university = universityNameInput.value.trim();
            const domain = domainNameInput.value.trim();
            let isValid = true;

            if (!university) {
                universityError.textContent = '学校名称不能为空。';
                universityError.classList.remove('hidden');
                isValid = false;
            }
            if (!domain) {
                domainError.textContent = '域名不能为空。';
                domainError.classList.remove('hidden');
                isValid = false;
            }
            if (!isValid) return;

            const isDomainValid = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(domain);
            if (!isDomainValid) {
                domainError.textContent = '请输入有效的域名格式。';
                domainError.classList.remove('hidden');
                isValid = false;
            }

            const universityExists = whitelistData.some(item => item.university.toLowerCase() === university.toLowerCase() && item.id != id);
            if (universityExists) {
                universityError.textContent = '该学校名称已存在。';
                universityError.classList.remove('hidden');
                isValid = false;
            }

            const domainExists = whitelistData.some(item => item.domain.toLowerCase() === domain.toLowerCase() && item.id != id);
            if (domainExists) {
                domainError.textContent = '该域名已存在。';
                domainError.classList.remove('hidden');
                isValid = false;
            }
            
            if (!isValid) return;

            if (id) { // Edit mode
                const index = whitelistData.findIndex(item => item.id == id);
                if (index > -1) {
                    whitelistData[index] = { ...whitelistData[index], university, domain, updatedAt: getCurrentDateTime() };
                }
            } else { // Add mode
                const newId = whitelistData.length > 0 ? Math.max(...whitelistData.map(item => item.id)) + 1 : 1;
                whitelistData.push({ id: newId, university, domain, updatedAt: getCurrentDateTime() });
            }
            renderTable(whitelistData);
            closeModal();
        });

        importForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const importError = document.getElementById('import-error');
            importError.classList.add('hidden');
            const file = csvFileInput.files[0];

            if (!file) {
                importError.textContent = '请选择一个CSV文件。';
                importError.classList.remove('hidden');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(event) {
                const csvData = event.target.result;
                const lines = csvData.split('\n');
                const newEntries = [];
                let currentMaxId = whitelistData.length > 0 ? Math.max(...whitelistData.map(item => item.id)) : 0;
                
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;
                    
                    const parts = line.split(',');
                    if (parts.length !== 2) {
                        importError.textContent = `文件第 ${i + 1} 行格式错误，应为：学校名称,域名`;
                        importError.classList.remove('hidden');
                        return;
                    }
                    
                    const university = parts[0].trim();
                    const domain = parts[1].trim();

                    if (!university || !domain) {
                        importError.textContent = `文件第 ${i + 1} 行数据不完整。`;
                        importError.classList.remove('hidden');
                        return;
                    }
                    
                    const domainExists = whitelistData.some(item => item.domain.toLowerCase() === domain.toLowerCase());
                    if (domainExists) {
                        console.warn(`跳过已存在的域名: ${domain}`);
                        continue;
                    }

                    newEntries.push({
                        id: ++currentMaxId,
                        university,
                        domain,
                        updatedAt: getCurrentDateTime()
                    });
                }
                
                if (newEntries.length > 0) {
                    whitelistData.push(...newEntries);
                    renderTable(whitelistData);
                }
                alert(`成功导入 ${newEntries.length} 条新纪录。`);
                closeImportModal();
            };
            
            reader.onerror = function() {
                importError.textContent = '读取文件时发生错误。';
                importError.classList.remove('hidden');
            };

            reader.readAsText(file);
        });

        tableBody.addEventListener('click', (e) => {
            if (e.target.classList.contains('edit-btn')) {
                const id = e.target.dataset.id;
                const data = whitelistData.find(item => item.id == id);
                openModal('edit', data);
            }
            if (e.target.classList.contains('delete-btn')) {
                const id = parseInt(e.target.dataset.id);
                openDeleteModal(id);
            }
        });
        
        document.getElementById('confirm-delete-btn').addEventListener('click', () => {
            if (Array.isArray(deleteTarget)) {
                whitelistData = whitelistData.filter(item => !deleteTarget.includes(item.id));
            } else {
                whitelistData = whitelistData.filter(item => item.id !== deleteTarget);
            }
            renderTable(whitelistData);
            closeDeleteModal();
        });
        
        document.getElementById('search-btn').addEventListener('click', () => {
            const query = document.getElementById('search-input').value.toLowerCase();
            const filteredData = whitelistData.filter(item => 
                item.university.toLowerCase().includes(query) || 
                item.domain.toLowerCase().includes(query)
            );
            renderTable(filteredData);
        });

        document.getElementById('reset-btn').addEventListener('click', () => {
            document.getElementById('search-input').value = '';
            renderTable(whitelistData);
        });
        
        document.getElementById('select-all').addEventListener('change', (e) => {
            const checkboxes = tableBody.querySelectorAll('.row-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = e.target.checked);
        });
        
        document.getElementById('batch-delete-btn').addEventListener('click', () => {
            const selectedIds = Array.from(tableBody.querySelectorAll('.row-checkbox:checked')).map(cb => parseInt(cb.value));
            if (selectedIds.length === 0) {
                alert('请至少选择一项进行删除。');
                return;
            }
            openDeleteModal(selectedIds);
        });

        renderTable(whitelistData);
    });
    </script>
</body>
</html>
